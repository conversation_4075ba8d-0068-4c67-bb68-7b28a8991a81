import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import FileUpload from './components/FileUpload';
import ContentDisplay from './components/ContentDisplay';
import XiabianSelector from './components/XiabianSelector';
import UsageCounter from './components/UsageCounter';
import { uploadAndAnalyze, uploadAndAnalyzeText, chatWithXiabian, isTextFile } from './services/api';
import './components/components.css';

// 在開發模式下引入測試輔助函數
if (import.meta.env.DEV) {
  import('./utils/testHelpers.js');
}

function App() {
  const [selectedXiabian, setSelectedXiabian] = useState('JU');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [content, setContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);
  const [currentFile, setCurrentFile] = useState(null);
  const [threadId, setThreadId] = useState(null);


  const handleFileUpload = useCallback(async (file) => {
    setIsUploading(true);
    setUploadProgress(0);
    setError(null);
    setCurrentFile(file);

    try {
      let analysisResult;

      // 根據檔案類型選擇不同的處理方式
      if (isTextFile(file)) {
        // 處理文字檔案
        analysisResult = await uploadAndAnalyzeText(file, (progress) => {
          setUploadProgress(progress);
        });
      } else {
        // 處理檔案（圖片）
        analysisResult = await uploadAndAnalyze(file, (progress) => {
          setUploadProgress(progress);
        });
      }

      setIsUploading(false);

      if (analysisResult.success) {
        // 開始生成內容
        await generateContent(analysisResult);
      } else {
        setError(analysisResult.error || '檔案分析失敗');
      }
    } catch (err) {
      setIsUploading(false);
      setError(err.message);
    }
  }, [selectedXiabian]);

  const generateContent = useCallback(async (analysisData) => {
    setIsGenerating(true);
    setError(null);

    try {
      // 構建提示訊息
      const prompt = buildPrompt(analysisData, selectedXiabian);

      // 與選定的小編對話
      const chatResult = await chatWithXiabian(selectedXiabian, prompt, threadId);

      if (chatResult.success) {
        setContent(chatResult.response);
        setThreadId(chatResult.thread_id);
      } else {
        setError(chatResult.error || '內容生成失敗');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsGenerating(false);
    }
  }, [selectedXiabian, threadId]);

  const buildPrompt = (analysisData, xiabianType) => {
    let basePrompt;

    // 根據分析結果類型構建不同的提示
    if (analysisData.file_type === 'markdown' || analysisData.file_type === 'text') {
      // 文字檔案的處理
      basePrompt = `請根據以下文字內容，創作一篇精彩的部落格文章：

檔案名稱：${analysisData.file_name}
檔案類型：${analysisData.file_type.toUpperCase()}
內容摘要：${analysisData.analysis_summary}

原始內容：
${analysisData.content}

請以 Markdown 格式撰寫，包含：
1. 吸引人的標題
2. 引人入勝的開頭
3. 對原始內容的深度分析和延伸
4. 實用的建議或心得
5. 精彩的結尾

`;
    } else {
      // 檔案分析的處理
      basePrompt = `請根據以下檔案分析結果，創作一篇精彩的部落格文章：

分析結果：
${JSON.stringify(analysisData.data, null, 2)}

請以 Markdown 格式撰寫，包含：
1. 吸引人的標題
2. 引人入勝的開頭
3. 詳細的內容分析
4. 實用的建議或心得
5. 精彩的結尾

`;
    }



    // 預設 prompt
    if (xiabianType === 'A') {
      return basePrompt + `
請以專業、深度的寫作風格創作，展現您的專業知識和獨特見解。
文章應該結構清晰、邏輯嚴謹，適合追求高品質內容的讀者。`;
    } else {
      return basePrompt + `
請以輕鬆親切、活潑熱情的語調創作，就像和好朋友聊天一樣自然。
多使用生活化的語言和表情符號，讓讀者感受到溫暖和親近感。`;
    }
  };



  const handleXiabianChange = useCallback(async (newXiabian) => {
    if (newXiabian === selectedXiabian || !currentFile) return;

    setSelectedXiabian(newXiabian);

    // 如果已有分析結果，重新生成內容
    if (content) {
      // 重新分析並生成內容
      try {
        const analysisResult = await uploadAndAnalyze(currentFile);
        if (analysisResult.success) {
          await generateContent(analysisResult);
        }
      } catch (err) {
        setError(err.message);
      }
    }
  }, [selectedXiabian, currentFile, content, generateContent]);

  return (
    <div className="app-container">
      <motion.header
        className="app-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1>AI 小編對話系統</h1>
        <p>上傳圖片，讓 AI 小編為您創作精彩的部落格內容</p>
      </motion.header>

      <motion.main
        className="main-content"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="left-panel">
          <UsageCounter />

          <XiabianSelector
            selectedXiabian={selectedXiabian}
            onXiabianChange={handleXiabianChange}
            disabled={!currentFile}
          />

          <FileUpload
            onFileUpload={handleFileUpload}
            isUploading={isUploading}
            uploadProgress={uploadProgress}
          />
        </div>

        <div className="right-panel">
          <ContentDisplay
            content={content}
            xiabianType={selectedXiabian}
            isLoading={isGenerating}
            error={error}
          />
        </div>
      </motion.main>
    </div>
  );
}

export default App;
