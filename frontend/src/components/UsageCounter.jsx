import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { BarChart3, Refresh<PERSON>w, AlertTriangle } from 'lucide-react';
import { getUsageInfo } from '../services/api';

const UsageCounter = () => {
  const [usageInfo, setUsageInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchUsageInfo = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getUsageInfo();
      if (response.success) {
        setUsageInfo(response);
      } else {
        setError('無法取得使用次數資訊');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsageInfo();
  }, []);

  if (loading) {
    return (
      <div className="usage-counter loading">
        <RefreshCw className="animate-spin" size={16} />
        <span>載入中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="usage-counter error">
        <AlertTriangle size={16} />
        <span>載入失敗</span>
      </div>
    );
  }

  if (!usageInfo) return null;

  const { current_usage, max_usage, remaining_usage } = usageInfo;
  const usagePercentage = (current_usage / max_usage) * 100;
  const isNearLimit = remaining_usage <= 5;
  const isAtLimit = remaining_usage <= 0;

  return (
    <motion.div
      className={`usage-counter ${isAtLimit ? 'at-limit' : isNearLimit ? 'near-limit' : ''}`}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="usage-header">
        <BarChart3 size={16} />
        <span className="usage-title">使用次數</span>
      </div>
      
      <div className="usage-info">
        <div className="usage-text">
          <span className="current">{current_usage}</span>
          <span className="separator">/</span>
          <span className="max">{max_usage}</span>
        </div>
        <div className="remaining">
          剩餘 {remaining_usage} 次
        </div>
      </div>

      <div className="usage-bar">
        <motion.div
          className="usage-progress"
          initial={{ width: 0 }}
          animate={{ width: `${usagePercentage}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>

      {isAtLimit && (
        <motion.div
          className="usage-warning"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <AlertTriangle size={14} />
          <span>已達使用上限</span>
        </motion.div>
      )}
    </motion.div>
  );
};

export default UsageCounter;
