/**
 * API 服務模組
 * 處理與後端 FastAPI 服務的所有通訊
 */

// 統一使用部署的後端地址
const API_BASE_URL = 'https://uat.heph-ai.net/api/v1/langlive';

/**
 * 通用 API 請求函數
 */
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail?.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API 請求失敗 (${endpoint}):`, error);
    throw error;
  }
}

/**
 * 上傳檔案並分析（圖片）
 */
export async function uploadAndAnalyze(file, onProgress = null) {
  const formData = new FormData();
  formData.append('image', file);

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    // 監聽上傳進度
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentComplete = (event.loaded / event.total) * 100;
          onProgress(percentComplete);
        }
      });
    }

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (error) {
          reject(new Error('回應格式錯誤'));
        }
      } else {
        try {
          const errorResponse = JSON.parse(xhr.responseText);
          reject(new Error(errorResponse.detail?.error || `上傳失敗: ${xhr.status}`));
        } catch {
          reject(new Error(`上傳失敗: ${xhr.status}`));
        }
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('網路錯誤'));
    });

    xhr.open('POST', `${API_BASE_URL}/api/v1/analyze`);
    xhr.send(formData);
  });
}

/**
 * 與小編對話
 */
export async function chatWithXiabian(xiabianType, message, threadId = null) {
  const endpoint = xiabianType === 'A' ? '/api/v1/xiabian-a/chat' : '/api/v1/xiabian-ju/chat';

  return apiRequest(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      message,
      thread_id: threadId,
    }),
  });
}

/**
 * 獲取系統配置
 */
export async function getConfig() {
  return apiRequest('/api/v1/config');
}

/**
 * 健康檢查
 */
export async function healthCheck() {
  return apiRequest('/health', { method: 'GET' });
}

/**
 * 取得使用次數資訊
 */
export async function getUsageInfo() {
  return apiRequest('/api/v1/usage', { method: 'GET' });
}

/**
 * 重製使用次數
 */
export async function resetUsage() {
  return apiRequest('/api/v1/usage/reset', { method: 'POST' });
}

/**
 * 上傳檔案並分析（文字）
 */
export async function uploadAndAnalyzeText(file, onProgress = null) {
  const formData = new FormData();
  formData.append('file', file);

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    // 監聽上傳進度
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentComplete = (event.loaded / event.total) * 100;
          onProgress(percentComplete);
        }
      });
    }

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (error) {
          reject(new Error('回應格式錯誤'));
        }
      } else {
        try {
          const errorResponse = JSON.parse(xhr.responseText);
          reject(new Error(errorResponse.detail?.error || `上傳失敗: ${xhr.status}`));
        } catch {
          reject(new Error(`上傳失敗: ${xhr.status}`));
        }
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('網路錯誤'));
    });

    xhr.open('POST', `${API_BASE_URL}/api/v1/analyze-text`);
    xhr.send(formData);
  });
}

/**
 * 檔案類型驗證
 */
export function validateFile(file) {
  const allowedImageTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/webp'];
  const allowedTextTypes = ['text/markdown', 'text/plain'];
  const maxSize = 2 * 1024 * 1024; // 2MB

  // 檢查是否為支援的圖片格式
  const isImage = allowedImageTypes.includes(file.type);

  // 檢查是否為支援的文字格式（根據副檔名）
  const fileName = file.name.toLowerCase();
  const isMarkdown = fileName.endsWith('.md');
  const isText = fileName.endsWith('.txt');
  const isDoc = fileName.endsWith('.doc');
  const isDocx = fileName.endsWith('.docx');
  const isTextFile = isMarkdown || isText || isDoc || isDocx || allowedTextTypes.includes(file.type);

  if (!isImage && !isTextFile) {
    throw new Error('不支援的檔案格式。請上傳 PNG、JPG、JPEG、WEBP 格式的檔案，或 Markdown (.md)、純文字 (.txt)、Word (.doc, .docx) 檔案。');
  }

  if (file.size > maxSize) {
    throw new Error('檔案大小超過限制。請上傳小於 2MB 的檔案。');
  }

  return true;
}

/**
 * 檢查檔案是否為文字檔案
 */
export function isTextFile(file) {
  const fileName = file.name.toLowerCase();
  const isMarkdown = fileName.endsWith('.md');
  const isText = fileName.endsWith('.txt');
  const allowedTextTypes = ['text/markdown', 'text/plain'];

  return isMarkdown || isText || allowedTextTypes.includes(file.type);
}

/**
 * 格式化檔案大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
